import React from 'react';
import {
  ZoomIn,
  ZoomOut,
  Download,
  Upload,
  RotateCcw,
  Sun,
  Moon
} from 'lucide-react';
import { ViewportState } from '../types';
import { Theme } from '../hooks/useTheme';
import { ModelSelector } from './ModelSelector';
import { ApiKeyInput } from './ApiKeyInput.tsx';
import { QuickGuide } from './QuickGuide';

interface ToolbarProps {
  viewport: ViewportState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  onExport: () => void;
  onImport: () => void;
  theme: Theme;
  onToggleTheme: () => void;
  apiKey: string;
  onSetApiKey: (key: string) => void;
  selectedModel: string;
  onSetSelectedModel: (model: string) => void;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  viewport,
  onZoomIn,
  onZoomOut,
  onResetView,
  onExport,
  onImport,
  theme,
  onToggleTheme,
  apiKey,
  onSetApiKey,
  selectedModel,
  onSetSelectedModel,
}) => {
  const handleImportClick = () => {
    onImport();
  };

  return (
    <>
      <div className="px-3 py-2 flex items-center gap-2 w-full h-[42px]">
        {/* Canvas Actions */}
        <div className="flex items-center gap-1">
          <button
            onClick={onExport}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Export Mind Map"
          >
            <Download size={16} />
          </button>
          <button
            onClick={handleImportClick}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Import Mind Map"
          >
            <Upload size={16} />
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* View Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={onZoomOut}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Zoom Out"
          >
            <ZoomOut size={16} />
          </button>
          <span className="text-sm font-mono text-gray-600 dark:text-gray-400 min-w-[4rem] text-center">
            {Math.round(viewport.zoom * 100)}%
          </span>
          <button
            onClick={onZoomIn}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Zoom In"
          >
            <ZoomIn size={16} />
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        <button
          onClick={onResetView}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title="Reset View"
        >
          <RotateCcw size={16} />
        </button>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        <button
          onClick={onToggleTheme}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title={`Switch to ${theme === 'dark' ? 'Light' : 'Dark'} Mode`}
        >
          {theme === 'dark' ? <Sun size={16} /> : <Moon size={16} />}
        </button>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* Model Selector */}
        <ModelSelector
          selectedModel={selectedModel}
          onModelChange={onSetSelectedModel}
          disabled={!apiKey}
        />

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* API Key Input */}
        <ApiKeyInput apiKey={apiKey} onSetApiKey={onSetApiKey} />

        <div className="flex-grow" />

        <div className="text-sm text-gray-600 dark:text-gray-400 mr-2">
          Made with ♥️ by{' '}
          <a 
            href="https://bagusfarisa.com" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Guntur
          </a>
        </div>

        <QuickGuide />
      </div>

    </>
  );
};