import React, { useState, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import {
  ChevronDown,
  ChevronRight,
  Plus,
  X,
  Search,
  Loader2,
  Globe,
  RotateCcw,
  ExternalLink,
  Check,
  Copy,
  CheckCheck
} from 'lucide-react';
import { NodeData } from '../types';
import { generateGeminiResponse } from '../utils/gemini';
import { copyToClipboard, formatTextForClipboard } from '../utils/clipboard';
import { NodeQueryForm } from './NodeQueryForm';
import { useDragOptimization } from '../hooks/useDragOptimization';

interface NodeProps {
  node: NodeData;
  nodes: Record<string, NodeData>; // Add nodes to get parent context
  onUpdateNode: (nodeId: string, updates: Partial<NodeData>) => void;
  onSetNodeQuery: (nodeId: string, query: string, response: string, sources?: Array<{title: string; uri: string}>) => void;
  onClearNodeQuery: (nodeId: string) => void;
  onCreateChild: (parentId: string) => void;
  onDelete: (nodeId: string) => void;
  onSelect: (nodeId: string) => void;
  onToggleNodeSearchGrounding: (nodeId: string) => void;
  apiKey: string;
  selectedModel: string;
  isRoot: boolean;
}

export const Node: React.FC<NodeProps> = ({
  node,
  nodes,
  onUpdateNode,
  onSetNodeQuery,
  onClearNodeQuery,
  onCreateChild,
  onDelete,
  onSelect,
  onToggleNodeSearchGrounding,
  apiKey,
  selectedModel,
  isRoot,
}) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [tempTitle, setTempTitle] = useState(node.title);
  const [isGeneratingResponse, setIsGeneratingResponse] = useState(false);
  const [copyStatus, setCopyStatus] = useState<'idle' | 'copying' | 'success' | 'error'>('idle');
  const [showCopyOptions, setShowCopyOptions] = useState(false);

  // Update tempTitle when node.title changes (important for preventing stale state)
  React.useEffect(() => {
    setTempTitle(node.title);
  }, [node.title]);

  // Optimized drag functionality
  const {
    elementRef: nodeRef,
    isDragging,
    startDrag,
    updateDrag,
    endDrag
  } = useDragOptimization({
    initialX: node.x,
    initialY: node.y,
    onUpdate: (x, y) => {
      // Only update position coordinates to preserve all other node data
      // Add validation to ensure we have valid coordinates
      if (typeof x === 'number' && typeof y === 'number' &&
          !isNaN(x) && !isNaN(y)) {
        onUpdateNode(node.id, { x, y });
      } else {
        console.warn(`Invalid coordinates for node ${node.id}:`, { x, y });
      }
    },
    onDragStart: () => onSelect(node.id),
    onDragEnd: () => {
      // Additional cleanup or validation can be added here if needed
    },
    throttleDelay: 16 // ~60fps
  });

  // Close copy options when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (_event: MouseEvent) => {
      if (showCopyOptions) {
        setShowCopyOptions(false);
      }
    };

    if (showCopyOptions) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showCopyOptions]);

  const handleMouseDown = (e: React.MouseEvent) => {
    // Don't start drag if clicking on interactive elements
    const target = e.target as HTMLElement;
    const isInteractiveElement =
      target.closest('button') ||
      target.closest('input') ||
      target.closest('textarea');

    if (!isInteractiveElement) {
      startDrag(e);
    }
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', updateDrag, { passive: false });
      document.addEventListener('mouseup', endDrag);
      document.addEventListener('mouseleave', endDrag); // Handle mouse leaving window
      return () => {
        document.removeEventListener('mousemove', updateDrag);
        document.removeEventListener('mouseup', endDrag);
        document.removeEventListener('mouseleave', endDrag);
      };
    }
  }, [isDragging, updateDrag, endDrag]);

  // Reset transform when node position changes externally (not during drag)
  React.useEffect(() => {
    if (!isDragging && nodeRef.current) {
      nodeRef.current.style.transform = '';
    }
  }, [node.x, node.y, isDragging]);

  const handleTitleSubmit = () => {
    onUpdateNode(node.id, { title: tempTitle });
    setIsEditingTitle(false);
  };

  // Get parent context for child nodes
  const getParentContext = (): string => {
    if (!node.parentId || !nodes[node.parentId]) return '';
    
    const parent = nodes[node.parentId];
    let context = '';
    
    if (parent.query && parent.response) {
      context += `Parent node context:\n`;
      context += `Question: ${parent.query}\n`;
      context += `Answer: ${parent.response}\n\n`;
      context += `Based on this context, please answer the following related question:\n`;
    }
    
    return context;
  };

  const handleSubmitQuery = async (query: string) => {
    if (!query || isGeneratingResponse || node.hasQueried) return;

    setIsGeneratingResponse(true);

    try {
      const parentContext = getParentContext();
      const fullQuery = parentContext + query;
      
      const response = await generateGeminiResponse(fullQuery, apiKey, node.searchGrounding, selectedModel);
      onSetNodeQuery(node.id, query, response.content, response.sources);
    } catch {
      onSetNodeQuery(node.id, query, 'Sorry, I encountered an error. Please try again.', undefined);
    } finally {
      setIsGeneratingResponse(false);
    }
  };



  const handleClearQuery = () => {
    onClearNodeQuery(node.id);
    // Don't clear the query input, keep it for the next question
  };

  const toggleExpanded = () => {
    onUpdateNode(node.id, { isExpanded: !node.isExpanded });
  };

  const handleSourceClick = (uri: string) => {
    if (uri && uri.startsWith('http')) {
      window.open(uri, '_blank', 'noopener,noreferrer');
    }
  };

  const handleCopyResponse = async (preserveMarkdown: boolean = false) => {
    if (!node.response) return;

    setCopyStatus('copying');
    setShowCopyOptions(false); // Close options menu

    try {
      // Include sources if they exist
      const textToCopy = formatTextForClipboard(
        node.response,
        preserveMarkdown,
        node.sources
      );
      const result = await copyToClipboard(textToCopy);

      if (result.success) {
        setCopyStatus('success');
        // Reset status after 2 seconds
        setTimeout(() => setCopyStatus('idle'), 2000);
      } else {
        setCopyStatus('error');
        console.error('Copy failed:', result.error);
        // Reset status after 3 seconds for error
        setTimeout(() => setCopyStatus('idle'), 3000);
      }
    } catch (error) {
      setCopyStatus('error');
      console.error('Copy error:', error);
      setTimeout(() => setCopyStatus('idle'), 3000);
    }
  };

  const nodeHeight = node.isExpanded ? (node.hasQueried ? 'auto' : 200) : 60;

  return (
    <div
      ref={nodeRef}
      className={`absolute bg-white dark:bg-gray-800 rounded-lg shadow-xl border transition-all duration-200 cursor-move select-none ${
        node.isSelected
          ? 'border-blue-500 shadow-blue-500/20'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
      } ${isDragging ? 'z-50' : ''}`}
      style={{
        left: node.x,
        top: node.y,
        width: node.width,
        minHeight: nodeHeight,
        willChange: isDragging ? 'transform' : 'auto', // Optimize for animations
        transition: isDragging ? 'none' : 'transform 0.2s ease',
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Header - Entire header is draggable */}
      <div 
        className="flex items-center p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750 rounded-t-lg cursor-move"
        onMouseDown={handleMouseDown}
      >
        <button
          onClick={toggleExpanded}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white transition-colors mr-2 flex-shrink-0"
          style={{
            opacity: node.childIds.length > 0 ? 1 : 0.5,
            width: '16px',
            height: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}
          title={node.isExpanded ? 'Collapse' : 'Expand'}
        >
          {node.isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        </button>
        <div className="flex-1 min-w-0 flex items-center">
          {isEditingTitle ? (
            <div className="flex-1 relative">
              <input
                type="text"
                value={tempTitle}
                onChange={(e) => setTempTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleTitleSubmit();
                  if (e.key === 'Escape') setIsEditingTitle(false);
                }}
                className="bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded px-2 py-1 text-sm w-full border border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:outline-none pr-8"
                autoFocus
              />
              <button
                onClick={handleTitleSubmit}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded transition-colors"
                title="Save changes"
              >
                <Check size={16} />
              </button>
            </div>
          ) : (
            <h3
              className="text-gray-900 dark:text-white font-medium text-sm cursor-text truncate"
              onClick={() => setIsEditingTitle(true)}
              title={node.title}
            >
              {node.title}
            </h3>
          )}
          <div className="flex items-center gap-1 ml-2">
            {node.hasQueried && (
              <div className="w-2 h-2 bg-green-500 rounded-full" title="Has query" />
            )}
            {isGeneratingResponse && (
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="Generating response" />
            )}
          </div>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => onToggleNodeSearchGrounding(node.id)}
            className={`p-1 rounded transition-colors ${
              node.searchGrounding
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            title={`Search Grounding ${node.searchGrounding ? 'On' : 'Off'}`}
          >
            <Search size={14} />
          </button>
          <button
            onClick={() => onCreateChild(node.id)}
            className="text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors p-1"
            title="Add child"
          >
            <Plus size={14} />
          </button>
          {!isRoot && (
            <button
              onClick={() => onDelete(node.id)}
              className="text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors p-1"
              title="Delete node"
            >
              <X size={14} />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      {node.isExpanded && (
        <div className="p-3">
          {!node.hasQueried ? (
            <NodeQueryForm 
              node={node} 
              nodes={nodes} 
              isGeneratingResponse={isGeneratingResponse} 
              onSubmit={handleSubmitQuery} 
            />
          ) : (
            /* Query and Response Display */
            <div className="space-y-4">
              {/* User Query */}
              <div className="bg-blue-50 dark:bg-blue-600/20 border border-blue-200 dark:border-blue-600/30 rounded-lg p-3">
                <div className="text-xs text-blue-700 dark:text-blue-400 font-medium mb-1">Your Question:</div>
                <div className="text-gray-900 dark:text-white text-sm">{node.query}</div>
              </div>

              {/* AI Response with Markdown */}
              <div className="bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">AI Response:</div>
                  <div className="relative">
                    <div className="flex items-center">
                      <button
                        onClick={() => handleCopyResponse(false)}
                        disabled={copyStatus === 'copying'}
                        className={`p-1 rounded-l transition-all duration-200 ${
                          copyStatus === 'success'
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                            : copyStatus === 'error'
                            ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-600'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                        title={
                          copyStatus === 'success'
                            ? 'Copied to clipboard!'
                            : copyStatus === 'error'
                            ? 'Failed to copy'
                            : copyStatus === 'copying'
                            ? 'Copying...'
                            : node.sources && node.sources.length > 0
                            ? 'Copy as plain text (includes sources)'
                            : 'Copy as plain text'
                        }
                      >
                        {copyStatus === 'copying' ? (
                          <Loader2 size={14} className="animate-spin" />
                        ) : copyStatus === 'success' ? (
                          <CheckCheck size={14} />
                        ) : (
                          <Copy size={14} />
                        )}
                      </button>
                      <button
                        onClick={() => setShowCopyOptions(!showCopyOptions)}
                        disabled={copyStatus === 'copying'}
                        className={`p-1 rounded-r border-l border-gray-300 dark:border-gray-500 transition-all duration-200 ${
                          copyStatus === 'success'
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                            : copyStatus === 'error'
                            ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-600'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                        title="Copy options"
                      >
                        <ChevronDown size={12} />
                      </button>
                    </div>

                    {showCopyOptions && (
                      <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-10 min-w-[160px]">
                        <button
                          onClick={() => handleCopyResponse(false)}
                          className="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg transition-colors"
                        >
                          {node.sources && node.sources.length > 0
                            ? 'Copy as plain text + sources'
                            : 'Copy as plain text'
                          }
                        </button>
                        <button
                          onClick={() => handleCopyResponse(true)}
                          className="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-b-lg transition-colors"
                        >
                          {node.sources && node.sources.length > 0
                            ? 'Copy with markdown + sources'
                            : 'Copy with markdown'
                          }
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-gray-900 dark:text-gray-100 text-sm leading-relaxed prose prose-gray dark:prose-invert prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      // Custom styling for markdown elements
                      h1: ({ children }) => <h1 className="text-lg font-bold text-gray-900 dark:text-white mb-2 mt-3 first:mt-0">{children}</h1>,
                      h2: ({ children }) => <h2 className="text-base font-bold text-gray-900 dark:text-white mb-2 mt-3 first:mt-0">{children}</h2>,
                      h3: ({ children }) => <h3 className="text-sm font-bold text-gray-900 dark:text-white mb-1 mt-2 first:mt-0">{children}</h3>,
                      p: ({ children }) => <p className="text-gray-900 dark:text-gray-100 mb-2 last:mb-0">{children}</p>,
                      ul: ({ children }) => <ul className="list-disc list-inside text-gray-900 dark:text-gray-100 mb-2 space-y-1">{children}</ul>,
                      ol: ({ children }) => <ol className="list-decimal list-inside text-gray-900 dark:text-gray-100 mb-2 space-y-1">{children}</ol>,
                      li: ({ children }) => <li className="text-gray-900 dark:text-gray-100">{children}</li>,
                      strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                      em: ({ children }) => <em className="italic text-gray-700 dark:text-gray-200">{children}</em>,
                      code: ({ children }) => <code className="bg-gray-200 dark:bg-gray-800 text-blue-700 dark:text-blue-300 px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                      pre: ({ children }) => <pre className="bg-gray-200 dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-2 rounded text-xs font-mono overflow-x-auto mb-2">{children}</pre>,
                      blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-400 dark:border-gray-500 pl-3 text-gray-700 dark:text-gray-300 italic mb-2">{children}</blockquote>,
                      a: ({ href, children }) => (
                        <a 
                          href={href} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline decoration-dotted hover:decoration-solid transition-all"
                        >
                          {children}
                        </a>
                      ),
                      hr: () => <hr className="border-gray-300 dark:border-gray-600 my-3" />,
                    }}
                  >
                    {node.response || ''}
                  </ReactMarkdown>
                </div>
              </div>

              {/* Sources - Clean list at bottom */}
              {node.sources && node.sources.length > 0 && (
                <div className="bg-gray-100 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                  <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 font-medium mb-3">
                    <Globe size={12} />
                    Sources:
                  </div>
                  <div className="space-y-2">
                    {node.sources.map((source, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5 flex-shrink-0">
                          {index + 1}.
                        </div>
                        {source.uri && source.uri.startsWith('http') ? (
                          <button
                            onClick={() => handleSourceClick(source.uri)}
                            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline decoration-dotted hover:decoration-solid transition-all text-left break-all flex items-start gap-1"
                          >
                            <ExternalLink size={10} className="flex-shrink-0 mt-0.5" />
                            <span>{source.title || source.uri}</span>
                          </button>
                        ) : (
                          <div className="text-xs text-gray-600 dark:text-gray-400 break-all">
                            {source.title || 'Search-grounded response'}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Clear Button */}
              <div className="flex justify-end">
                <button
                  onClick={handleClearQuery}
                  className="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg px-3 py-1 text-xs transition-colors flex items-center gap-1"
                >
                  <RotateCcw size={12} />
                  Ask New Question
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};