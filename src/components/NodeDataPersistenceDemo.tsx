/**
 * Demo component to test node data persistence during drag operations
 * This component helps verify that the fix for data loss during node movement works correctly
 */

import React, { useState } from 'react';
import { useMindMap } from '../hooks/useMindMap';

export const NodeDataPersistenceDemo: React.FC = () => {
  const mindMapHook = useMindMap();
  const { data, createChildNode, updateNode, setNodeQuery } = mindMapHook;
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testNodeId, setTestNodeId] = useState<string | null>(null);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // Monitor node data changes during test
  React.useEffect(() => {
    if (isTestRunning && testNodeId && data?.nodes[testNodeId]) {
      const node = data.nodes[testNodeId];
      console.log(`📊 Node ${testNodeId} state changed:`, {
        query: node.query,
        response: node.response ? `Present (${node.response.substring(0, 50)}...)` : 'Missing',
        sources: node.sources?.length || 0,
        hasQueried: node.hasQueried,
        x: node.x,
        y: node.y,
        timestamp: new Date().toISOString()
      });
    }
  }, [data, testNodeId, isTestRunning]);

  const runDataPersistenceTest = async () => {
    if (!data) {
      addTestResult('❌ No data available');
      return;
    }

    setIsTestRunning(true);
    addTestResult('🧪 Starting node data persistence test...');
    console.log('🧪 Test starting with data:', data);

    // Step 1: Create a child node
    const childNodeId = createChildNode(data.rootNodeId);
    if (!childNodeId) {
      addTestResult('❌ Failed to create child node');
      setIsTestRunning(false);
      return;
    }
    setTestNodeId(childNodeId);
    addTestResult(`✅ Created child node: ${childNodeId}`);
    console.log('✅ Created child node:', childNodeId, data.nodes[childNodeId]);

    // Step 2: Add query and response data
    const testQuery = 'What is the meaning of life?';
    const testResponse = 'The meaning of life is 42, according to The Hitchhiker\'s Guide to the Galaxy.';
    const testSources = [{ title: 'Test Source', uri: 'https://example.com' }];

    setNodeQuery(childNodeId, testQuery, testResponse, testSources);
    addTestResult('✅ Added query and response data to child node');
    console.log('✅ After setNodeQuery:', data.nodes[childNodeId]);

    // Wait a moment for state to update
    await new Promise(resolve => setTimeout(resolve, 100));

    // Step 3: Verify data was set - get fresh data
    const currentData = mindMapHook.data;
    const nodeWithData = currentData?.nodes[childNodeId];
    console.log('🔍 Verifying node data:', nodeWithData);
    if (nodeWithData?.query === testQuery && nodeWithData?.response === testResponse) {
      addTestResult('✅ Query and response data verified');
    } else {
      addTestResult('❌ Query and response data not set correctly');
      addTestResult(`   Expected query: "${testQuery}"`);
      addTestResult(`   Actual query: "${nodeWithData?.query || 'undefined'}"`);
      addTestResult(`   Expected response: "${testResponse.substring(0, 50)}..."`);
      addTestResult(`   Actual response: "${nodeWithData?.response?.substring(0, 50) || 'undefined'}..."`);
      return;
    }

    // Step 4: Simulate drag operations with multiple position updates
    const positions = [
      { x: 500, y: 350 },
      { x: 550, y: 400 },
      { x: 600, y: 450 },
      { x: 650, y: 500 },
      { x: 700, y: 550 },
    ];

    addTestResult('🔄 Simulating drag operations...');
    
    for (let i = 0; i < positions.length; i++) {
      const { x, y } = positions[i];
      const beforeData = mindMapHook.data?.nodes[childNodeId];
      console.log(`🔄 Position update ${i + 1}: Before update`, beforeData);

      updateNode(childNodeId, { x, y });

      // Small delay to simulate real drag timing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if data is still preserved - get fresh data
      const currentData = mindMapHook.data;
      const updatedNode = currentData?.nodes[childNodeId];
      console.log(`🔄 Position update ${i + 1}: After update`, updatedNode);

      if (updatedNode?.query === testQuery && updatedNode?.response === testResponse) {
        addTestResult(`✅ Position update ${i + 1}: Data preserved (x: ${x}, y: ${y})`);
      } else {
        addTestResult(`❌ Position update ${i + 1}: Data lost! (x: ${x}, y: ${y})`);
        addTestResult(`   Query: ${updatedNode?.query || 'MISSING'}`);
        addTestResult(`   Response: ${updatedNode?.response ? 'Present' : 'MISSING'}`);
        addTestResult(`   HasQueried: ${updatedNode?.hasQueried || 'MISSING'}`);
        console.error('❌ Data loss detected:', {
          before: beforeData,
          after: updatedNode,
          expectedQuery: testQuery,
          expectedResponse: testResponse
        });
        return;
      }
    }

    // Step 5: Final verification - get fresh data
    const finalData = mindMapHook.data;
    const finalNode = finalData?.nodes[childNodeId];
    if (finalNode?.query === testQuery &&
        finalNode?.response === testResponse &&
        finalNode?.sources?.length === 1 &&
        finalNode?.hasQueried === true) {
      addTestResult('🎉 TEST PASSED: All node data preserved during drag operations!');
    } else {
      addTestResult('❌ TEST FAILED: Node data was corrupted');
      console.error('❌ Final verification failed:', {
        finalNode,
        expectedQuery: testQuery,
        expectedResponse: testResponse,
        expectedSources: testSources
      });
    }

    setIsTestRunning(false);
    setTestNodeId(null);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runManualDragTest = async () => {
    if (!data) {
      addTestResult('❌ No data available');
      return;
    }

    addTestResult('🎯 Running manual drag simulation test...');

    // Find an existing child node or create one
    const existingChild = Object.values(data.nodes).find(node => node.parentId);
    let nodeId = existingChild?.id;

    if (!nodeId) {
      nodeId = createChildNode(data.rootNodeId);
      if (!nodeId) {
        addTestResult('❌ Failed to create test node');
        return;
      }
      addTestResult('✅ Created test node for manual drag test');
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Add some test data if the node doesn't have any
    const node = data.nodes[nodeId];
    if (!node.hasQueried) {
      setNodeQuery(nodeId, 'Test question for drag test', 'Test response that should be preserved during drag operations', [{ title: 'Test Source', uri: 'https://example.com' }]);
      addTestResult('✅ Added test data to node');
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    addTestResult('🎯 Node is ready for manual drag test');
    addTestResult('👆 Try dragging the node around and check if data is preserved');
    addTestResult(`📍 Test node ID: ${nodeId}`);

    // Set up monitoring for this node
    setTestNodeId(nodeId);
    setIsTestRunning(true);

    // Auto-stop monitoring after 30 seconds
    setTimeout(() => {
      setIsTestRunning(false);
      setTestNodeId(null);
      addTestResult('⏰ Manual test monitoring stopped after 30 seconds');
    }, 30000);
  };

  const runComprehensiveNodeTest = async () => {
    if (!data) {
      addTestResult('❌ No data available');
      return;
    }

    addTestResult('🔬 Starting comprehensive node test (Parent + Child)...');

    // Test 1: Parent Node (Root Node) with data
    addTestResult('📋 Test 1: Testing parent node (root) data preservation...');

    const rootNodeId = data.rootNodeId;
    const parentQuery = 'Parent node test query';
    const parentResponse = 'This is a parent node response that should be preserved during drag operations.';
    const parentSources = [{ title: 'Parent Source', uri: 'https://parent.example.com' }];

    // Add data to parent node
    setNodeQuery(rootNodeId, parentQuery, parentResponse, parentSources);
    addTestResult('✅ Added test data to parent node');

    await new Promise(resolve => setTimeout(resolve, 200));

    // Verify parent initial state
    const initialParent = mindMapHook.data?.nodes[rootNodeId];
    if (!initialParent?.hasQueried) {
      addTestResult('❌ Parent initial data verification failed');
      return;
    }

    addTestResult('✅ Parent initial data verified');
    console.log('🔬 Initial parent state:', initialParent);

    // Test parent drag
    addTestResult('🔄 Testing parent node drag...');
    console.log('🔬 Before parent drag:', mindMapHook.data?.nodes[rootNodeId]);

    updateNode(rootNodeId, { x: 500, y: 350 });

    await new Promise(resolve => setTimeout(resolve, 100));

    const afterParentDrag = mindMapHook.data?.nodes[rootNodeId];
    console.log('🔬 After parent drag:', afterParentDrag);

    if (afterParentDrag?.query === parentQuery && afterParentDrag?.response === parentResponse) {
      addTestResult('✅ Parent node: Data preserved during drag');
    } else {
      addTestResult('❌ Parent node: Data lost during drag!');
      addTestResult(`   Expected query: "${parentQuery}"`);
      addTestResult(`   Actual query: "${afterParentDrag?.query || 'MISSING'}"`);
      addTestResult(`   Expected response: Present`);
      addTestResult(`   Actual response: ${afterParentDrag?.response ? 'Present' : 'MISSING'}`);
    }

    // Test 2: Child Node with data
    addTestResult('📋 Test 2: Testing child node data preservation...');

    const childNodeId = createChildNode(data.rootNodeId);
    if (!childNodeId) {
      addTestResult('❌ Failed to create child node');
      return;
    }

    await new Promise(resolve => setTimeout(resolve, 100));

    const childQuery = 'Child node test query';
    const childResponse = 'This is a child node response that should be preserved during drag operations.';
    const childSources = [{ title: 'Child Source', uri: 'https://child.example.com' }];

    setNodeQuery(childNodeId, childQuery, childResponse, childSources);
    addTestResult('✅ Added test data to child node');

    await new Promise(resolve => setTimeout(resolve, 200));

    // Verify child initial state
    const initialChild = mindMapHook.data?.nodes[childNodeId];
    if (!initialChild?.hasQueried) {
      addTestResult('❌ Child initial data verification failed');
      return;
    }

    addTestResult('✅ Child initial data verified');
    console.log('🔬 Initial child state:', initialChild);

    // Test child drag
    addTestResult('🔄 Testing child node drag...');
    console.log('🔬 Before child drag:', mindMapHook.data?.nodes[childNodeId]);

    updateNode(childNodeId, { x: 800, y: 500 });

    await new Promise(resolve => setTimeout(resolve, 100));

    const afterChildDrag = mindMapHook.data?.nodes[childNodeId];
    console.log('🔬 After child drag:', afterChildDrag);

    if (afterChildDrag?.query === childQuery && afterChildDrag?.response === childResponse) {
      addTestResult('✅ Child node: Data preserved during drag');
    } else {
      addTestResult('❌ Child node: Data lost during drag!');
      addTestResult(`   Expected query: "${childQuery}"`);
      addTestResult(`   Actual query: "${afterChildDrag?.query || 'MISSING'}"`);
      addTestResult(`   Expected response: Present`);
      addTestResult(`   Actual response: ${afterChildDrag?.response ? 'Present' : 'MISSING'}`);
    }

    // Test 3: Multiple rapid drags on both nodes
    addTestResult('📋 Test 3: Testing rapid drag operations...');

    const rapidPositions = [
      { x: 600, y: 400 },
      { x: 650, y: 450 },
      { x: 700, y: 500 }
    ];

    for (let i = 0; i < rapidPositions.length; i++) {
      const { x, y } = rapidPositions[i];

      // Drag parent
      updateNode(rootNodeId, { x: x - 100, y: y - 100 });
      // Drag child
      updateNode(childNodeId, { x, y });

      await new Promise(resolve => setTimeout(resolve, 50));

      const parentCheck = mindMapHook.data?.nodes[rootNodeId];
      const childCheck = mindMapHook.data?.nodes[childNodeId];

      if (parentCheck?.query === parentQuery && childCheck?.query === childQuery) {
        addTestResult(`✅ Rapid drag ${i + 1}: Both nodes preserved data`);
      } else {
        addTestResult(`❌ Rapid drag ${i + 1}: Data lost!`);
        addTestResult(`   Parent query: ${parentCheck?.query ? 'Present' : 'MISSING'}`);
        addTestResult(`   Child query: ${childCheck?.query ? 'Present' : 'MISSING'}`);
      }
    }

    addTestResult('🎯 Comprehensive test completed');
  };

  return (
    <div className="fixed top-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 shadow-lg max-w-md max-h-96 overflow-y-auto z-50">
      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
        Node Data Persistence Test
      </h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={runDataPersistenceTest}
          disabled={isTestRunning}
          className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isTestRunning ? 'Running Test...' : 'Run Automated Test'}
        </button>

        <button
          onClick={runManualDragTest}
          className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Setup Manual Drag Test
        </button>

        <button
          onClick={runComprehensiveNodeTest}
          className="w-full px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          Run Comprehensive Test
        </button>

        <button
          onClick={clearResults}
          className="w-full px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Clear Results
        </button>
      </div>

      <div className="space-y-1">
        <h4 className="font-medium text-gray-900 dark:text-white">Test Results:</h4>
        <div className="text-sm space-y-1 max-h-48 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 italic">No tests run yet</p>
          ) : (
            testResults.map((result, index) => (
              <div
                key={index}
                className={`p-1 rounded text-xs ${
                  result.includes('❌') 
                    ? 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300'
                    : result.includes('✅') || result.includes('🎉')
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300'
                    : result.includes('🔄') || result.includes('🧪')
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                }`}
              >
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
        <p>This test verifies that node query/response data is preserved during drag operations.</p>
      </div>
    </div>
  );
};
