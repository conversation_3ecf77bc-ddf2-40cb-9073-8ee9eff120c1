import React, { useState, useRef } from 'react';
import { useDragOptimization } from '../hooks/useDragOptimization';
import { dragPerformanceMonitor, getBrowserPerformanceInfo } from '../utils/performance';

interface DemoNodeProps {
  id: string;
  x: number;
  y: number;
  title: string;
  onUpdate: (id: string, x: number, y: number) => void;
}

const DemoNode: React.FC<DemoNodeProps> = ({ id, x, y, title, onUpdate }) => {
  // Default viewport for demo (no zoom functionality in demo)
  const defaultViewport = { x: 0, y: 0, zoom: 1 };

  const {
    elementRef,
    isDragging,
    startDrag,
    updateDrag,
    endDrag
  } = useDragOptimization({
    initialX: x,
    initialY: y,
    viewport: defaultViewport,
    onUpdate: (newX, newY) => onUpdate(id, newX, newY),
    throttleDelay: 16
  });

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    startDrag(e);
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', updateDrag, { passive: false });
      document.addEventListener('mouseup', endDrag);
      return () => {
        document.removeEventListener('mousemove', updateDrag);
        document.removeEventListener('mouseup', endDrag);
      };
    }
  }, [isDragging, updateDrag, endDrag]);

  return (
    <div
      ref={elementRef}
      className={`absolute bg-blue-500 text-white rounded-lg p-4 cursor-move select-none transition-all duration-200 ${
        isDragging ? 'shadow-2xl z-50' : 'shadow-lg'
      }`}
      style={{
        left: x,
        top: y,
        width: 120,
        height: 80,
        willChange: isDragging ? 'transform' : 'auto',
      }}
      onMouseDown={handleMouseDown}
    >
      <div className="text-sm font-medium">{title}</div>
      <div className="text-xs opacity-75 mt-1">
        {Math.round(x)}, {Math.round(y)}
      </div>
    </div>
  );
};

export const DragPerformanceDemo: React.FC = () => {
  const [nodes, setNodes] = useState([
    { id: '1', x: 100, y: 100, title: 'Node 1' },
    { id: '2', x: 250, y: 150, title: 'Node 2' },
    { id: '3', x: 400, y: 200, title: 'Node 3' },
    { id: '4', x: 150, y: 300, title: 'Node 4' },
    { id: '5', x: 350, y: 350, title: 'Node 5' },
  ]);

  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);
  const [browserInfo] = useState(getBrowserPerformanceInfo());
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const updateNode = (id: string, x: number, y: number) => {
    setNodes(prev => prev.map(node => 
      node.id === id ? { ...node, x, y } : node
    ));
  };

  const addRandomNodes = () => {
    const newNodes = Array.from({ length: 10 }, (_, i) => ({
      id: `random-${Date.now()}-${i}`,
      x: Math.random() * 600,
      y: Math.random() * 400,
      title: `Node ${nodes.length + i + 1}`
    }));
    setNodes(prev => [...prev, ...newNodes]);
  };

  const clearNodes = () => {
    setNodes([]);
  };

  const startPerformanceMonitoring = () => {
    if (metricsIntervalRef.current) return;
    
    metricsIntervalRef.current = setInterval(() => {
      const metrics = dragPerformanceMonitor.getMetrics();
      if (metrics) {
        setPerformanceMetrics(metrics);
      }
    }, 100);
  };

  const stopPerformanceMonitoring = () => {
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
      metricsIntervalRef.current = null;
      setPerformanceMetrics(null);
    }
  };

  React.useEffect(() => {
    return () => {
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="w-full h-screen bg-gray-100 dark:bg-gray-900 relative overflow-hidden">
      {/* Control Panel */}
      <div className="absolute top-4 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-10 max-w-sm">
        <h3 className="text-lg font-bold mb-3 text-gray-900 dark:text-white">
          Drag Performance Demo
        </h3>
        
        <div className="space-y-3">
          <div className="flex gap-2">
            <button
              onClick={addRandomNodes}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
            >
              Add 10 Nodes
            </button>
            <button
              onClick={clearNodes}
              className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
            >
              Clear All
            </button>
          </div>

          <div className="flex gap-2">
            <button
              onClick={startPerformanceMonitoring}
              className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
            >
              Start Monitoring
            </button>
            <button
              onClick={stopPerformanceMonitoring}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Stop Monitoring
            </button>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            <div>Nodes: {nodes.length}</div>
            <div>Hardware Acceleration: {browserInfo.hardwareAcceleration ? '✅' : '❌'}</div>
            <div>Device Memory: {browserInfo.deviceMemory}GB</div>
            <div>CPU Cores: {browserInfo.hardwareConcurrency}</div>
          </div>

          {performanceMetrics && (
            <div className="text-sm bg-gray-50 dark:bg-gray-700 p-2 rounded">
              <div className="font-medium text-gray-900 dark:text-white mb-1">Live Metrics:</div>
              <div className="text-gray-600 dark:text-gray-300">
                <div>FPS: {Math.round(performanceMetrics.averageFPS)}</div>
                <div>Frames: {performanceMetrics.frameCount}</div>
                <div>Time: {Math.round(performanceMetrics.totalTime)}ms</div>
                <div>Min Frame: {Math.round(performanceMetrics.minFrameTime * 100) / 100}ms</div>
                <div>Max Frame: {Math.round(performanceMetrics.maxFrameTime * 100) / 100}ms</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-10 max-w-xs">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Instructions:</h4>
        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• Drag nodes around to test performance</li>
          <li>• Add more nodes to stress test</li>
          <li>• Monitor FPS during dragging</li>
          <li>• Check browser console for detailed metrics</li>
        </ul>
      </div>

      {/* Demo Area */}
      <div className="absolute inset-0 pt-20">
        {nodes.map(node => (
          <DemoNode
            key={node.id}
            id={node.id}
            x={node.x}
            y={node.y}
            title={node.title}
            onUpdate={updateNode}
          />
        ))}
      </div>

      {/* Performance Indicator */}
      {performanceMetrics && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 text-white px-3 py-2 rounded text-sm font-mono">
          FPS: {Math.round(performanceMetrics.averageFPS)} | 
          Frames: {performanceMetrics.frameCount}
        </div>
      )}
    </div>
  );
};
