/**
 * Utility functions for validating and preserving node data integrity
 */

import { NodeData } from '../types';

/**
 * Validates that a node has all required properties
 */
export const validateNodeData = (node: NodeData): boolean => {
  if (!node || typeof node !== 'object') {
    return false;
  }

  const requiredFields = ['id', 'x', 'y', 'title', 'isExpanded', 'childIds', 'isSelected', 'width', 'height', 'searchGrounding', 'hasQueried'];
  
  for (const field of requiredFields) {
    if (!(field in node)) {
      console.warn(`Node missing required field: ${field}`, node);
      return false;
    }
  }

  // Validate data types
  if (typeof node.id !== 'string' ||
      typeof node.x !== 'number' ||
      typeof node.y !== 'number' ||
      typeof node.title !== 'string' ||
      typeof node.isExpanded !== 'boolean' ||
      !Array.isArray(node.childIds) ||
      typeof node.isSelected !== 'boolean' ||
      typeof node.width !== 'number' ||
      typeof node.height !== 'number' ||
      typeof node.searchGrounding !== 'boolean' ||
      typeof node.hasQueried !== 'boolean') {
    console.warn('Node has invalid data types', node);
    return false;
  }

  // Validate numeric values
  if (isNaN(node.x) || isNaN(node.y) || isNaN(node.width) || isNaN(node.height)) {
    console.warn('Node has invalid numeric values', node);
    return false;
  }

  return true;
};

/**
 * Safely merges node updates while preserving existing data
 */
export const safeNodeUpdate = (existingNode: NodeData, updates: Partial<NodeData>): NodeData => {
  if (!validateNodeData(existingNode)) {
    console.error('Existing node data is invalid, cannot safely update', existingNode);
    throw new Error('Invalid existing node data');
  }

  // Create the updated node
  const updatedNode = { ...existingNode, ...updates };

  // Special handling for drag operations - only validate if not a simple position update
  const isPositionOnlyUpdate = Object.keys(updates).length <= 2 &&
    ('x' in updates || 'y' in updates) &&
    Object.keys(updates).every(key => key === 'x' || key === 'y');

  if (isPositionOnlyUpdate) {
    // For position-only updates, just validate the coordinates
    if (typeof updatedNode.x === 'number' && typeof updatedNode.y === 'number' &&
        !isNaN(updatedNode.x) && !isNaN(updatedNode.y)) {
      return updatedNode;
    } else {
      console.warn('Invalid position coordinates in drag update, preserving existing position', {
        updates,
        existingPosition: { x: existingNode.x, y: existingNode.y }
      });
      return existingNode;
    }
  }

  // Validate the result for non-position updates
  if (!validateNodeData(updatedNode)) {
    console.error('Updated node data would be invalid, reverting to original', {
      existing: existingNode,
      updates,
      result: updatedNode
    });
    return existingNode; // Return original if update would create invalid data
  }

  return updatedNode;
};

/**
 * Checks if node data contains query/response information
 */
export const hasQueryData = (node: NodeData): boolean => {
  return !!(node.query || node.response || node.sources?.length || node.hasQueried);
};

/**
 * Logs node data changes for debugging
 */
export const logNodeDataChange = (nodeId: string, before: NodeData, after: NodeData, operation: string): void => {
  if (import.meta.env.DEV) {
    const beforeHasQuery = hasQueryData(before);
    const afterHasQuery = hasQueryData(after);

    if (beforeHasQuery && !afterHasQuery) {
      console.warn(`⚠️ Node ${nodeId} lost query data during ${operation}:`, {
        before: {
          query: before.query,
          response: before.response?.substring(0, 100) + '...',
          sources: before.sources,
          hasQueried: before.hasQueried
        },
        after: {
          query: after.query,
          response: after.response?.substring(0, 100) + '...',
          sources: after.sources,
          hasQueried: after.hasQueried
        }
      });
    } else if (beforeHasQuery && afterHasQuery) {
      console.log(`✅ Node ${nodeId} preserved query data during ${operation}`);
    }

    // Additional validation for position updates
    if (operation === 'updateNode' && ('x' in after || 'y' in after)) {
      if (before.title !== after.title ||
          before.query !== after.query ||
          before.response !== after.response ||
          before.hasQueried !== after.hasQueried) {
        console.warn(`⚠️ Node ${nodeId} had unexpected data changes during position update:`, {
          titleChanged: before.title !== after.title,
          queryChanged: before.query !== after.query,
          responseChanged: before.response !== after.response,
          hasQueriedChanged: before.hasQueried !== after.hasQueried
        });
      }
    }
  }
};

/**
 * Creates a backup of critical node data before operations
 */
export const createNodeDataBackup = (node: NodeData) => {
  return {
    id: node.id,
    query: node.query,
    response: node.response,
    sources: node.sources,
    hasQueried: node.hasQueried,
    title: node.title,
    messages: [...node.messages]
  };
};

/**
 * Restores critical node data from backup if current data is corrupted
 */
export const restoreFromBackup = (currentNode: NodeData, backup: ReturnType<typeof createNodeDataBackup>): NodeData => {
  if (!hasQueryData(currentNode) && hasQueryData(backup as any)) {
    console.log(`🔄 Restoring query data for node ${backup.id} from backup`);
    return {
      ...currentNode,
      query: backup.query,
      response: backup.response,
      sources: backup.sources,
      hasQueried: backup.hasQueried,
      title: backup.title,
      messages: backup.messages
    };
  }
  return currentNode;
};
