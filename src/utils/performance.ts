/**
 * Performance monitoring utilities for drag operations
 */

interface PerformanceMetrics {
  frameCount: number;
  totalTime: number;
  averageFPS: number;
  minFrameTime: number;
  maxFrameTime: number;
}

class DragPerformanceMonitor {
  private frameCount = 0;
  private startTime = 0;
  private lastFrameTime = 0;
  private frameTimes: number[] = [];
  private isMonitoring = false;

  start() {
    this.frameCount = 0;
    this.startTime = performance.now();
    this.lastFrameTime = this.startTime;
    this.frameTimes = [];
    this.isMonitoring = true;
  }

  recordFrame() {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    const frameTime = currentTime - this.lastFrameTime;
    
    this.frameCount++;
    this.frameTimes.push(frameTime);
    this.lastFrameTime = currentTime;
  }

  stop(): PerformanceMetrics {
    if (!this.isMonitoring) {
      return {
        frameCount: 0,
        totalTime: 0,
        averageFPS: 0,
        minFrameTime: 0,
        maxFrameTime: 0
      };
    }

    this.isMonitoring = false;
    const endTime = performance.now();
    const totalTime = endTime - this.startTime;
    const averageFPS = this.frameCount / (totalTime / 1000);
    const minFrameTime = Math.min(...this.frameTimes);
    const maxFrameTime = Math.max(...this.frameTimes);

    return {
      frameCount: this.frameCount,
      totalTime,
      averageFPS,
      minFrameTime,
      maxFrameTime
    };
  }

  getMetrics(): PerformanceMetrics | null {
    if (!this.isMonitoring) return null;

    const currentTime = performance.now();
    const totalTime = currentTime - this.startTime;
    const averageFPS = this.frameCount / (totalTime / 1000);
    const minFrameTime = this.frameTimes.length > 0 ? Math.min(...this.frameTimes) : 0;
    const maxFrameTime = this.frameTimes.length > 0 ? Math.max(...this.frameTimes) : 0;

    return {
      frameCount: this.frameCount,
      totalTime,
      averageFPS,
      minFrameTime,
      maxFrameTime
    };
  }
}

// Global instance for easy access
export const dragPerformanceMonitor = new DragPerformanceMonitor();

/**
 * Utility to measure function execution time
 */
export const measureExecutionTime = <T extends (...args: any[]) => any>(
  fn: T,
  label?: string
): T => {
  return ((...args: any[]) => {
    const start = performance.now();
    const result = fn(...args);
    const end = performance.now();
    
    if (label) {
      console.log(`${label} execution time: ${(end - start).toFixed(2)}ms`);
    }
    
    return result;
  }) as T;
};

/**
 * Debounce utility for performance optimization
 */
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): T => {
  let timeoutId: NodeJS.Timeout;
  
  return ((...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T;
};

/**
 * RequestAnimationFrame-based throttle for smooth animations
 */
export const rafThrottle = <T extends (...args: any[]) => void>(func: T): T => {
  let rafId: number | null = null;
  let lastArgs: any[] | null = null;

  return ((...args: any[]) => {
    lastArgs = args;
    
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        if (lastArgs) {
          func(...lastArgs);
        }
        rafId = null;
        lastArgs = null;
      });
    }
  }) as T;
};

/**
 * Check if the browser supports hardware acceleration
 */
export const supportsHardwareAcceleration = (): boolean => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  return !!gl;
};

/**
 * Get browser performance information
 */
export const getBrowserPerformanceInfo = () => {
  const nav = navigator as any;
  
  return {
    hardwareAcceleration: supportsHardwareAcceleration(),
    deviceMemory: nav.deviceMemory || 'unknown',
    hardwareConcurrency: nav.hardwareConcurrency || 'unknown',
    connection: nav.connection ? {
      effectiveType: nav.connection.effectiveType,
      downlink: nav.connection.downlink,
      rtt: nav.connection.rtt
    } : 'unknown'
  };
};
