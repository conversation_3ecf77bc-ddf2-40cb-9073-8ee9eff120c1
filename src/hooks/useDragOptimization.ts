import { useRef, useCallback } from 'react';
import { dragPerformanceMonitor } from '../utils/performance';
import { ViewportState } from '../types';

// Throttle utility for performance optimization
const throttle = <T extends (...args: any[]) => void>(func: T, delay: number): T => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return ((...args: any[]) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  }) as T;
};

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  offsetX: number;
  offsetY: number;
}

interface UseDragOptimizationProps {
  initialX: number;
  initialY: number;
  viewport: ViewportState;
  onUpdate: (x: number, y: number) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  throttleDelay?: number;
  dragSensitivity?: number; // Multiplier for overall drag sensitivity (default: 1.0)
}

export const useDragOptimization = ({
  initialX,
  initialY,
  viewport,
  onUpdate,
  onDragStart,
  onDragEnd,
  throttleDelay = 16, // ~60fps
  dragSensitivity = 1.0 // Default sensitivity multiplier
}: UseDragOptimizationProps) => {
  const dragStateRef = useRef<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    currentX: initialX,
    currentY: initialY,
    offsetX: 0,
    offsetY: 0
  });

  const elementRef = useRef<HTMLElement>(null);

  // Keep refs to the latest callback functions to avoid stale closures
  const onUpdateRef = useRef(onUpdate);
  const onDragStartRef = useRef(onDragStart);
  const onDragEndRef = useRef(onDragEnd);

  onUpdateRef.current = onUpdate;
  onDragStartRef.current = onDragStart;
  onDragEndRef.current = onDragEnd;

  // Throttled update function for better performance
  const throttledUpdate = useRef(
    throttle((x: number, y: number) => {
      // Validate coordinates before updating
      if (typeof x === 'number' && typeof y === 'number' &&
          !isNaN(x) && !isNaN(y)) {
        try {
          // Use the ref to get the latest onUpdate function
          onUpdateRef.current(x, y);
        } catch (error) {
          console.error('Error during drag position update:', error);
          // Don't throw - just log the error to prevent drag interruption
        }
      } else {
        console.warn('Throttled update received invalid coordinates:', { x, y });
      }
    }, throttleDelay)
  );

  const startDrag = useCallback((e: React.MouseEvent | MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Update drag state with simple mouse position tracking
    // We'll handle zoom compensation in updateDrag for better control
    dragStateRef.current = {
      isDragging: true,
      startX: e.clientX,
      startY: e.clientY,
      currentX: initialX,
      currentY: initialY,
      offsetX: 0, // Will be calculated during drag
      offsetY: 0  // Will be calculated during drag
    };

    // Start performance monitoring
    dragPerformanceMonitor.start();

    onDragStartRef.current?.();

    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
  }, [initialX, initialY]);

  const updateDrag = useCallback((e: MouseEvent) => {
    if (!dragStateRef.current.isDragging) return;

    e.preventDefault();

    // Record frame for performance monitoring
    dragPerformanceMonitor.recordFrame();

    // Calculate mouse movement in screen space
    const mouseDeltaX = e.clientX - dragStateRef.current.startX;
    const mouseDeltaY = e.clientY - dragStateRef.current.startY;

    // Apply zoom-aware sensitivity calculation
    // The key insight: we want consistent *perceived* movement, not coordinate movement
    // At zoom 0.5 (zoomed out), the canvas appears smaller, so we need slightly more movement
    // At zoom 2.0 (zoomed in), the canvas appears larger, so we need slightly less movement
    // But we don't want a 1:1 inverse relationship as that was too sensitive

    // Calculate a balanced zoom factor that provides consistent feel
    // This approach reduces the zoom effect while maintaining some compensation
    const zoomCompensation = Math.sqrt(1 / viewport.zoom); // Square root dampens the effect
    const effectiveSensitivity = dragSensitivity * zoomCompensation;

    // Apply the balanced sensitivity to mouse movement
    const adjustedDeltaX = mouseDeltaX * effectiveSensitivity;
    const adjustedDeltaY = mouseDeltaY * effectiveSensitivity;

    const newX = initialX + adjustedDeltaX;
    const newY = initialY + adjustedDeltaY;

    // Validate coordinates before proceeding
    if (typeof newX !== 'number' || typeof newY !== 'number' ||
        isNaN(newX) || isNaN(newY)) {
      console.warn('Invalid drag coordinates calculated, skipping update:', { newX, newY });
      return;
    }

    // Update current position in ref
    dragStateRef.current.currentX = newX;
    dragStateRef.current.currentY = newY;

    // Apply immediate visual feedback using transform
    if (elementRef.current) {
      const deltaX = newX - initialX;
      const deltaY = newY - initialY;
      // Apply translation without affecting the scale set by CSS
      elementRef.current.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(1.02)`;
    }

    // Throttled state update for performance - only call if coordinates are valid
    throttledUpdate.current(newX, newY);
  }, [initialX, initialY, viewport.zoom, dragSensitivity]);

  const endDrag = useCallback(() => {
    if (!dragStateRef.current.isDragging) return;

    // Stop performance monitoring
    dragPerformanceMonitor.stop();

    // Final position update - ensure we have valid coordinates
    const finalX = dragStateRef.current.currentX;
    const finalY = dragStateRef.current.currentY;

    if (typeof finalX === 'number' && typeof finalY === 'number' &&
        !isNaN(finalX) && !isNaN(finalY)) {
      try {
        // Force final update to ensure position is saved
        onUpdateRef.current(finalX, finalY);
      } catch (error) {
        console.error('Error during final drag position update:', error);
        // Try to fallback to initial position if final update fails
        try {
          onUpdateRef.current(initialX, initialY);
        } catch (fallbackError) {
          console.error('Fallback position update also failed:', fallbackError);
        }
      }
    } else {
      console.warn('Invalid drag coordinates detected, keeping original position:', {
        finalX,
        finalY,
        originalX: initialX,
        originalY: initialY
      });
    }

    // Reset drag state
    dragStateRef.current.isDragging = false;
    document.body.style.userSelect = '';

    // Reset transform
    if (elementRef.current) {
      elementRef.current.style.transform = '';
    }

    onDragEndRef.current?.();
  }, [initialX, initialY]);

  const isDragging = dragStateRef.current.isDragging;

  return {
    elementRef,
    isDragging,
    startDrag,
    updateDrag,
    endDrag,
    dragState: dragStateRef.current
  };
};
