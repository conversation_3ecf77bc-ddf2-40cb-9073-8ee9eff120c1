/**
 * Test suite for zoom-aware drag functionality
 * Verifies that drag sensitivity remains consistent across different zoom levels
 */

describe('Zoom-Aware Drag Functionality', () => {
  // Mock viewport states for testing
  const createViewport = (zoom: number) => ({ x: 0, y: 0, zoom });

  // Simulate drag calculation logic from useDragOptimization
  const calculateDragPosition = (
    initialX: number,
    initialY: number,
    startMouseX: number,
    startMouseY: number,
    currentMouseX: number,
    currentMouseY: number,
    zoom: number,
    dragSensitivity: number = 1.0
  ) => {
    const mouseDeltaX = currentMouseX - startMouseX;
    const mouseDeltaY = currentMouseY - startMouseY;

    // Apply the new balanced zoom compensation (square root dampening)
    const zoomCompensation = Math.sqrt(1 / zoom);
    const effectiveSensitivity = dragSensitivity * zoomCompensation;

    const adjustedDeltaX = mouseDeltaX * effectiveSensitivity;
    const adjustedDeltaY = mouseDeltaY * effectiveSensitivity;

    return {
      x: initialX + adjustedDeltaX,
      y: initialY + adjustedDeltaY
    };
  };

  test('should maintain consistent drag sensitivity at zoom 1.0', () => {
    const initialX = 100;
    const initialY = 100;
    const startMouseX = 150;
    const startMouseY = 150;
    const currentMouseX = 200;
    const currentMouseY = 200;
    const zoom = 1.0;

    const result = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, zoom
    );

    // At zoom 1.0, 50px mouse movement should result in 50px node movement
    expect(result.x).toBe(150); // 100 + 50
    expect(result.y).toBe(150); // 100 + 50
  });

  test('should increase drag sensitivity when zoomed out (zoom < 1)', () => {
    const initialX = 100;
    const initialY = 100;
    const startMouseX = 150;
    const startMouseY = 150;
    const currentMouseX = 200;
    const currentMouseY = 200;
    const zoom = 0.5; // Zoomed out

    const result = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, zoom
    );

    // At zoom 0.5, with square root dampening: sqrt(1/0.5) = sqrt(2) ≈ 1.414
    // 50px mouse movement * 1.414 ≈ 70.7px node movement
    expect(result.x).toBeCloseTo(170.7, 1); // 100 + (50 * sqrt(2))
    expect(result.y).toBeCloseTo(170.7, 1); // 100 + (50 * sqrt(2))
  });

  test('should decrease drag sensitivity when zoomed in (zoom > 1)', () => {
    const initialX = 100;
    const initialY = 100;
    const startMouseX = 150;
    const startMouseY = 150;
    const currentMouseX = 200;
    const currentMouseY = 200;
    const zoom = 2.0; // Zoomed in

    const result = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, zoom
    );

    // At zoom 2.0, with square root dampening: sqrt(1/2.0) = sqrt(0.5) ≈ 0.707
    // 50px mouse movement * 0.707 ≈ 35.4px node movement
    expect(result.x).toBeCloseTo(135.4, 1); // 100 + (50 * sqrt(0.5))
    expect(result.y).toBeCloseTo(135.4, 1); // 100 + (50 * sqrt(0.5))
  });

  test('should handle various zoom levels consistently', () => {
    const testCases = [
      { zoom: 0.1, expectedMultiplier: Math.sqrt(10) }, // sqrt(1/0.1) = sqrt(10) ≈ 3.16
      { zoom: 0.25, expectedMultiplier: Math.sqrt(4) }, // sqrt(1/0.25) = sqrt(4) = 2.0
      { zoom: 0.5, expectedMultiplier: Math.sqrt(2) },  // sqrt(1/0.5) = sqrt(2) ≈ 1.41
      { zoom: 1.0, expectedMultiplier: 1 },             // sqrt(1/1.0) = 1.0
      { zoom: 1.5, expectedMultiplier: Math.sqrt(1/1.5) }, // sqrt(1/1.5) ≈ 0.82
      { zoom: 2.0, expectedMultiplier: Math.sqrt(0.5) },   // sqrt(1/2.0) ≈ 0.71
      { zoom: 4.0, expectedMultiplier: Math.sqrt(0.25) },  // sqrt(1/4.0) = 0.5
    ];

    const initialX = 100;
    const initialY = 100;
    const startMouseX = 100;
    const startMouseY = 100;
    const currentMouseX = 150; // 50px movement
    const currentMouseY = 150; // 50px movement
    const mouseMovement = 50;

    testCases.forEach(({ zoom, expectedMultiplier }) => {
      const result = calculateDragPosition(
        initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, zoom
      );

      const expectedMovement = mouseMovement * expectedMultiplier;
      const expectedX = initialX + expectedMovement;
      const expectedY = initialY + expectedMovement;

      expect(result.x).toBeCloseTo(expectedX, 2);
      expect(result.y).toBeCloseTo(expectedY, 2);
    });
  });

  test('should handle negative mouse movements correctly', () => {
    const initialX = 200;
    const initialY = 200;
    const startMouseX = 200;
    const startMouseY = 200;
    const currentMouseX = 150; // -50px movement
    const currentMouseY = 150; // -50px movement
    const zoom = 0.5;

    const result = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, zoom
    );

    // At zoom 0.5, with square root dampening: sqrt(1/0.5) = sqrt(2) ≈ 1.414
    // -50px mouse movement * 1.414 ≈ -70.7px node movement
    expect(result.x).toBeCloseTo(129.3, 1); // 200 + (-50 * sqrt(2))
    expect(result.y).toBeCloseTo(129.3, 1); // 200 + (-50 * sqrt(2))
  });

  test('should handle extreme zoom values safely', () => {
    const initialX = 100;
    const initialY = 100;
    const startMouseX = 100;
    const startMouseY = 100;
    const currentMouseX = 110; // 10px movement
    const currentMouseY = 110; // 10px movement

    // Test very small zoom (extreme zoom out)
    const extremeZoomOut = 0.01;
    const resultZoomOut = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, extremeZoomOut
    );
    // sqrt(1/0.01) = sqrt(100) = 10, so 10px * 10 = 100px movement
    expect(resultZoomOut.x).toBe(200); // 100 + (10 * sqrt(100))
    expect(resultZoomOut.y).toBe(200);

    // Test very large zoom (extreme zoom in)
    const extremeZoomIn = 10.0;
    const resultZoomIn = calculateDragPosition(
      initialX, initialY, startMouseX, startMouseY, currentMouseX, currentMouseY, extremeZoomIn
    );
    // sqrt(1/10.0) = sqrt(0.1) ≈ 0.316, so 10px * 0.316 ≈ 3.16px movement
    expect(resultZoomIn.x).toBeCloseTo(103.16, 2); // 100 + (10 * sqrt(0.1))
    expect(resultZoomIn.y).toBeCloseTo(103.16, 2);
  });
});

export default {};
