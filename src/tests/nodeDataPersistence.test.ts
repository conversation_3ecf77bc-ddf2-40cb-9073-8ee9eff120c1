/**
 * Test suite for node data persistence during drag operations
 * Ensures that query, response, and other node data is preserved when nodes are moved
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useMindMap } from '../hooks/useMindMap';
import { NodeData } from '../types';

// Mock localStorage for tests
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Create initial root node for testing
const createRootNode = () => ({
  id: 'root',
  x: 400,
  y: 300,
  title: 'Main Topic',
  query: undefined,
  response: undefined,
  sources: undefined,
  messages: [],
  isExpanded: true,
  childIds: [],
  isSelected: false,
  width: 450,
  height: 200,
  searchGrounding: false,
  hasQueried: false,
});

// Mock the canvas manager
const mockCanvasManager = {
  activeCanvas: {
    id: 'test-canvas',
    name: 'Test Canvas',
    data: {
      nodes: { root: createRootNode() },
      rootNodeId: 'root',
      searchGrounding: false,
      selectedModel: 'gemini-2.0-flash-exp',
      version: 1,
      lastModified: Date.now(),
    },
    viewport: { x: 0, y: 0, zoom: 1 },
    createdAt: Date.now(),
    lastModified: Date.now(),
  },
  activeCanvasId: 'test-canvas',
  updateCanvas: vi.fn(),
};

vi.mock('../hooks/useCanvasManager', () => ({
  useCanvasManager: () => mockCanvasManager,
}));

// Additional localStorage mock setup
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('Node Data Persistence During Drag Operations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    // Reset the mock canvas data with root node
    mockCanvasManager.activeCanvas.data = {
      nodes: { root: createRootNode() },
      rootNodeId: 'root',
      searchGrounding: false,
      selectedModel: 'gemini-2.0-flash-exp',
      version: 1,
      lastModified: Date.now(),
    };
  });

  test('should preserve node query and response data when updating position', () => {
    const { result } = renderHook(() => useMindMap());

    // Create a child node with query and response data
    act(() => {
      result.current.createChildNode('root');
    });

    // Get the created node ID (it will be the only child of root)
    const nodeIds = Object.keys(result.current.data?.nodes || {});
    const childNodeId = nodeIds.find(id => id !== 'root');
    expect(childNodeId).toBeDefined();

    // Set query and response data
    const testQuery = 'What is artificial intelligence?';
    const testResponse = 'Artificial intelligence is a field of computer science...';
    const testSources = [{ title: 'AI Wikipedia', uri: 'https://en.wikipedia.org/wiki/Artificial_intelligence' }];

    act(() => {
      result.current.setNodeQuery(childNodeId!, testQuery, testResponse, testSources);
    });

    // Verify the data was set
    const nodeWithData = result.current.data?.nodes[childNodeId!];
    expect(nodeWithData?.query).toBe(testQuery);
    expect(nodeWithData?.response).toBe(testResponse);
    expect(nodeWithData?.sources).toEqual(testSources);
    expect(nodeWithData?.hasQueried).toBe(true);

    // Simulate drag operation by updating position
    const newX = 500;
    const newY = 400;

    act(() => {
      result.current.updateNode(childNodeId!, { x: newX, y: newY });
    });

    // Verify that position was updated but all other data is preserved
    const updatedNode = result.current.data?.nodes[childNodeId!];
    expect(updatedNode?.x).toBe(newX);
    expect(updatedNode?.y).toBe(newY);
    expect(updatedNode?.query).toBe(testQuery);
    expect(updatedNode?.response).toBe(testResponse);
    expect(updatedNode?.sources).toEqual(testSources);
    expect(updatedNode?.hasQueried).toBe(true);
    expect(updatedNode?.title).toBe('New Topic'); // Original title should be preserved
  });

  test('should handle multiple rapid position updates without data loss', () => {
    const { result } = renderHook(() => useMindMap());

    // Create a child node
    act(() => {
      result.current.createChildNode('root');
    });

    const nodeIds = Object.keys(result.current.data?.nodes || {});
    const childNodeId = nodeIds.find(id => id !== 'root');

    // Set query and response data
    const testQuery = 'Test query';
    const testResponse = 'Test response';

    act(() => {
      result.current.setNodeQuery(childNodeId!, testQuery, testResponse);
    });

    // Simulate rapid position updates (like during drag)
    const positions = [
      { x: 100, y: 100 },
      { x: 150, y: 120 },
      { x: 200, y: 140 },
      { x: 250, y: 160 },
      { x: 300, y: 180 },
    ];

    positions.forEach(({ x, y }) => {
      act(() => {
        result.current.updateNode(childNodeId!, { x, y });
      });
    });

    // Verify final position and data preservation
    const finalNode = result.current.data?.nodes[childNodeId!];
    expect(finalNode?.x).toBe(300);
    expect(finalNode?.y).toBe(180);
    expect(finalNode?.query).toBe(testQuery);
    expect(finalNode?.response).toBe(testResponse);
    expect(finalNode?.hasQueried).toBe(true);
  });

  test('should handle edge case of updating non-existent node gracefully', () => {
    const { result } = renderHook(() => useMindMap());

    // Try to update a non-existent node
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    act(() => {
      result.current.updateNode('non-existent-id', { x: 100, y: 100 });
    });

    // Should log a warning but not crash
    expect(consoleSpy).toHaveBeenCalledWith('Attempted to update non-existent node: non-existent-id');
    
    consoleSpy.mockRestore();
  });

  test('should preserve all node properties during position updates', () => {
    const { result } = renderHook(() => useMindMap());

    // Create a child node
    act(() => {
      result.current.createChildNode('root');
    });

    const nodeIds = Object.keys(result.current.data?.nodes || {});
    const childNodeId = nodeIds.find(id => id !== 'root');

    // Set comprehensive node data
    const nodeData = {
      title: 'Custom Title',
      query: 'Custom Query',
      response: 'Custom Response',
      sources: [{ title: 'Source', uri: 'http://example.com' }],
      isExpanded: false,
      width: 500,
      height: 300,
      searchGrounding: true,
      hasQueried: true,
    };

    act(() => {
      result.current.updateNode(childNodeId!, nodeData);
    });

    // Update position
    act(() => {
      result.current.updateNode(childNodeId!, { x: 600, y: 500 });
    });

    // Verify all properties are preserved
    const finalNode = result.current.data?.nodes[childNodeId!];
    expect(finalNode?.x).toBe(600);
    expect(finalNode?.y).toBe(500);
    expect(finalNode?.title).toBe(nodeData.title);
    expect(finalNode?.query).toBe(nodeData.query);
    expect(finalNode?.response).toBe(nodeData.response);
    expect(finalNode?.sources).toEqual(nodeData.sources);
    expect(finalNode?.isExpanded).toBe(nodeData.isExpanded);
    expect(finalNode?.width).toBe(nodeData.width);
    expect(finalNode?.height).toBe(nodeData.height);
    expect(finalNode?.searchGrounding).toBe(nodeData.searchGrounding);
    expect(finalNode?.hasQueried).toBe(nodeData.hasQueried);
  });
});
