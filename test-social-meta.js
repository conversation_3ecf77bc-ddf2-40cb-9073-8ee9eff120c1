#!/usr/bin/env node

/**
 * Test script to validate social media meta tags
 * This script checks if all required Open Graph and Twitter Card meta tags are present
 * and properly configured in the built HTML file.
 */

import fs from 'fs';
import path from 'path';

const htmlFilePath = path.join(process.cwd(), 'dist', 'index.html');

// Read the built HTML file
let htmlContent;
try {
  htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
} catch (error) {
  console.error('❌ Error reading HTML file:', error.message);
  console.log('💡 Make sure to run "npm run build" first');
  process.exit(1);
}

// Define required meta tags
const requiredMetaTags = {
  // Open Graph tags
  'og:type': 'website',
  'og:url': 'https://wondermap.xyz/',
  'og:title': 'WonderMap - BYOK AI Idea Canvas',
  'og:description': 'Explore ideas in your own terms. Privacy-focused AI idea exploration.',
  'og:image': 'https://wondermap.xyz/social-preview.jpg',
  'og:image:width': '1200',
  'og:image:height': '630',
  'og:image:type': 'image/jpeg',
  'og:site_name': 'WonderMap',
  
  // Twitter Card tags
  'twitter:card': 'summary_large_image',
  'twitter:url': 'https://wondermap.xyz/',
  'twitter:title': 'WonderMap - BYOK AI Idea Canvas',
  'twitter:description': 'Explore ideas in your own terms. Privacy-focused AI idea exploration.',
  'twitter:image': 'https://wondermap.xyz/social-preview.jpg',
  'twitter:image:alt': 'WonderMap - AI-powered idea exploration canvas',
  'twitter:site': '@wondermap',
  'twitter:creator': '@bagusfarisa'
};

console.log('🔍 Testing Social Media Meta Tags...\n');

let allTestsPassed = true;
let passedCount = 0;
let totalCount = Object.keys(requiredMetaTags).length;

// Test each required meta tag
for (const [property, expectedContent] of Object.entries(requiredMetaTags)) {
  const isOpenGraph = property.startsWith('og:');
  const isTwitter = property.startsWith('twitter:');
  
  let regex;
  if (isOpenGraph) {
    regex = new RegExp(`<meta\\s+property=["']${property}["']\\s+content=["']([^"']+)["']`, 'i');
  } else if (isTwitter) {
    regex = new RegExp(`<meta\\s+name=["']${property}["']\\s+content=["']([^"']+)["']`, 'i');
  }
  
  const match = htmlContent.match(regex);
  
  if (match && match[1] === expectedContent) {
    console.log(`✅ ${property}: ${match[1]}`);
    passedCount++;
  } else if (match) {
    console.log(`⚠️  ${property}: Found "${match[1]}" but expected "${expectedContent}"`);
    allTestsPassed = false;
  } else {
    console.log(`❌ ${property}: Missing`);
    allTestsPassed = false;
  }
}

// Additional checks
console.log('\n🔍 Additional Checks...\n');

// Check if image file exists
const imageExists = fs.existsSync(path.join(process.cwd(), 'dist', 'social-preview.jpg'));
if (imageExists) {
  console.log('✅ Social preview image exists in dist folder');
} else {
  console.log('❌ Social preview image missing from dist folder');
  allTestsPassed = false;
}

// Check for proper HTML structure
if (htmlContent.includes('<title>WonderMap | BYOK AI Idea Canvas</title>')) {
  console.log('✅ Page title is correct');
} else {
  console.log('❌ Page title is missing or incorrect');
  allTestsPassed = false;
}

// Summary
console.log('\n📊 Summary:');
console.log(`Passed: ${passedCount}/${totalCount} meta tags`);

if (allTestsPassed) {
  console.log('\n🎉 All tests passed! Social media previews should work correctly.');
  console.log('\n💡 Next steps:');
  console.log('1. Test with Facebook Sharing Debugger: https://developers.facebook.com/tools/debug/');
  console.log('2. Test with Twitter Card Validator: https://cards-dev.twitter.com/validator');
  console.log('3. Deploy and test with actual social media platforms');
} else {
  console.log('\n❌ Some tests failed. Please review the issues above.');
  process.exit(1);
}
