# Social Media Preview Implementation

## Overview

This document describes the social media preview functionality for WonderMap, including Open Graph and Twitter Card meta tags implementation, testing procedures, and troubleshooting guidelines.

## Issues Fixed

### 1. Twitter Card Type Error
**Problem**: The `twitter:card` meta tag was incorrectly set to `"social-preview.jpg"` instead of the proper card type.
**Solution**: Changed to `"summary_large_image"` which is the correct value for large image cards.

### 2. Relative Image URLs
**Problem**: Meta tags used relative URLs (`/social-preview.jpg`) which don't work for social media crawlers.
**Solution**: Updated to absolute URLs (`https://wondermap.xyz/social-preview.jpg`).

### 3. Missing Meta Tag Properties
**Problem**: Missing important meta tag properties for better social media compatibility.
**Solution**: Added comprehensive meta tags including image dimensions, alt text, and site information.

## Current Implementation

### Open Graph Meta Tags (Facebook, LinkedIn, etc.)
```html
<meta property="og:type" content="website">
<meta property="og:url" content="https://wondermap.xyz/">
<meta property="og:title" content="WonderMap - BYOK AI Idea Canvas">
<meta property="og:description" content="Explore ideas in your own terms. Privacy-focused AI idea exploration.">
<meta property="og:image" content="https://wondermap.xyz/social-preview.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:type" content="image/jpeg">
<meta property="og:site_name" content="WonderMap">
```

### Twitter Card Meta Tags
```html
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="https://wondermap.xyz/">
<meta name="twitter:title" content="WonderMap - BYOK AI Idea Canvas">
<meta name="twitter:description" content="Explore ideas in your own terms. Privacy-focused AI idea exploration.">
<meta name="twitter:image" content="https://wondermap.xyz/social-preview.jpg">
<meta name="twitter:image:alt" content="WonderMap - AI-powered idea exploration canvas">
<meta name="twitter:site" content="@wondermap">
<meta name="twitter:creator" content="@bagusfarisa">
```

## Social Preview Image

- **File**: `public/social-preview.jpg` (copied to `dist/social-preview.jpg` during build)
- **Dimensions**: 1200x630 pixels (optimal for social media)
- **Format**: JPEG
- **Size**: ~70KB

## Testing

### Automated Testing
Run the automated test script to validate all meta tags:

```bash
npm run test:social
```

This script checks:
- ✅ All required Open Graph meta tags
- ✅ All required Twitter Card meta tags
- ✅ Correct meta tag values
- ✅ Social preview image existence
- ✅ Proper HTML structure

### Manual Testing with Social Media Debuggers

#### Facebook Sharing Debugger
1. Visit: https://developers.facebook.com/tools/debug/
2. Enter URL: `https://wondermap.xyz/`
3. Click "Debug"
4. Verify all meta tags are detected correctly
5. Check that the preview image displays properly

#### Twitter Card Validator
1. Visit: https://cards-dev.twitter.com/validator
2. Enter URL: `https://wondermap.xyz/`
3. Click "Preview card"
4. Verify the large image card displays correctly
5. Check title, description, and image

#### LinkedIn Post Inspector
1. Visit: https://www.linkedin.com/post-inspector/
2. Enter URL: `https://wondermap.xyz/`
3. Click "Inspect"
4. Verify preview displays correctly

### Local Testing
1. Build the project: `npm run build`
2. Start preview server: `npm run preview`
3. Visit: `http://localhost:4173/`
4. View page source to verify meta tags

## Build Process Integration

The social media preview functionality is integrated into the build process:

1. **Development**: Meta tags are in `index.html`
2. **Build**: Vite copies `public/social-preview.jpg` to `dist/`
3. **Testing**: `npm run test:social` validates the built HTML
4. **Deployment**: All files are ready for production

## Troubleshooting

### Common Issues

#### 1. Image Not Loading
- **Symptom**: Social media shows broken image or no image
- **Check**: Verify `dist/social-preview.jpg` exists after build
- **Solution**: Ensure image is in `public/` directory before building

#### 2. Old Preview Cached
- **Symptom**: Social media shows old preview after changes
- **Solution**: Use social media debuggers to force refresh cache

#### 3. Meta Tags Not Detected
- **Symptom**: Social media debuggers don't find meta tags
- **Check**: Verify meta tags are in `<head>` section
- **Solution**: Run `npm run test:social` to validate

### Validation Checklist

Before deployment, ensure:
- [ ] `npm run build` completes successfully
- [ ] `npm run test:social` passes all tests
- [ ] Social preview image exists in `dist/` folder
- [ ] Meta tags use absolute URLs
- [ ] Image dimensions are 1200x630
- [ ] All required meta tags are present

## Future Improvements

1. **Dynamic Meta Tags**: Implement dynamic meta tags for different pages/canvases
2. **Multiple Images**: Support different images for different social platforms
3. **Structured Data**: Add JSON-LD structured data for better SEO
4. **Analytics**: Track social media referrals and engagement

## References

- [Open Graph Protocol](https://ogp.me/)
- [Twitter Cards Documentation](https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards)
- [Facebook Sharing Best Practices](https://developers.facebook.com/docs/sharing/best-practices)
- [LinkedIn Post Inspector](https://www.linkedin.com/help/linkedin/answer/a521928)
